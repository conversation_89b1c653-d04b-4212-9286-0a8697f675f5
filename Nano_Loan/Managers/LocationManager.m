#import "LocationManager.h"
#import "NetworkManager.h"
#import <CoreLocation/CoreLocation.h>

static NSString * const kCachedCoordinateKey = @"CachedCoordinate";
// 高敏感度定位配置 - 确保6位小数经纬度有细微变化
static const NSTimeInterval kLocationTimeout = 30.0; // 30秒超时，平衡精度和响应速度
static const CLLocationAccuracy kBestAccuracy = 0.5; // 最佳精度0.5米
static const CLLocationAccuracy kGoodAccuracy = 1.0; // 良好精度1米
static const CLLocationAccuracy kAcceptableAccuracy = 2.0; // 可接受精度2米
static const NSTimeInterval kLocationMaxAge = 0.1; // 位置数据最大有效期0.1秒（确保极高新鲜度）
static const NSTimeInterval kMinWaitTime = 5.0; // 最少等待5秒让GPS稳定

/// 单次定位请求对象
@interface LocationRequest : NSObject
@property (nonatomic, strong) CLLocationManager *locationManager;
@property (nonatomic, copy) LocationCompletionBlock completion;
@property (nonatomic, strong) NSTimer *timeoutTimer;
@property (nonatomic, assign) NSTimeInterval startTime;
@property (nonatomic, strong) CLLocation *bestLocation; // 当前最佳位置
@property (nonatomic, assign) BOOL hasReceivedFirstLocation; // 是否已收到第一个位置
@property (nonatomic, assign) NSInteger locationUpdateCount; // 位置更新次数
@end

@implementation LocationRequest
@end

@interface LocationManager () <CLLocationManagerDelegate>
@property (nonatomic, strong) NSMutableArray<LocationRequest *> *activeRequests;
@property (nonatomic, assign) CLLocationCoordinate2D cachedCoordinate;
// 防重复上报机制
@property (nonatomic, assign) NSTimeInterval lastReportTime;
@property (nonatomic, assign) BOOL isReporting;
@end

@implementation LocationManager

+ (instancetype)sharedManager {
    static LocationManager *instance;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        instance = [[LocationManager alloc] init];
    });
    return instance;
}

- (instancetype)init {
    self = [super init];
    if (self) {
        _activeRequests = [NSMutableArray array];
        _cachedCoordinate = [self loadCachedCoordinate];
        _lastReportTime = 0;
        _isReporting = NO;
    }
    return self;
}

#pragma mark - Public Methods

- (void)getCurrentLocationForTracking:(LocationCompletionBlock)completion {
    if (!completion) {
        return;
    }

    // 检查定位权限
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
    if (status == kCLAuthorizationStatusDenied || status == kCLAuthorizationStatusRestricted) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
        return;
    }

    // 创建新的定位请求
    LocationRequest *request = [[LocationRequest alloc] init];
    request.completion = completion;
    request.startTime = [[NSDate date] timeIntervalSince1970];

    // 创建新的CLLocationManager实例，配置最高精度
    request.locationManager = [[CLLocationManager alloc] init];
    request.locationManager.delegate = self;
    request.locationManager.desiredAccuracy = kCLLocationAccuracyBestForNavigation; // 最高精度
    request.locationManager.distanceFilter = kCLDistanceFilterNone; // 不过滤任何距离变化，捕获微小移动

    // iOS 14+ 临时精确位置权限
    if (@available(iOS 14.0, *)) {
        if (request.locationManager.accuracyAuthorization == CLAccuracyAuthorizationReducedAccuracy) {
            [request.locationManager requestTemporaryFullAccuracyAuthorizationWithPurposeKey:@"tracking" completion:nil];
        }
    }

    // 设置超时定时器
    __weak typeof(self) weakSelf = self;
    __weak typeof(request) weakRequest = request;
    request.timeoutTimer = [NSTimer scheduledTimerWithTimeInterval:kLocationTimeout repeats:NO block:^(NSTimer * _Nonnull timer) {
        [weakSelf handleLocationTimeout:weakRequest];
    }];

    // 添加到活跃请求列表
    [self.activeRequests addObject:request];

    // 请求权限（如果需要）并开始定位
    if (status == kCLAuthorizationStatusNotDetermined) {
        [request.locationManager requestWhenInUseAuthorization];
    } else {
        [request.locationManager startUpdatingLocation];
    }
}

- (CLLocationCoordinate2D)getCachedCoordinate {
    return self.cachedCoordinate;
}

/// 强制刷新定位缓存 - 用于需要最新定位的场景
- (void)forceRefreshLocationCache:(LocationCompletionBlock)completion {
    NSLog(@"[LocationManager] 🔄 强制刷新定位缓存");
    [self getCurrentLocationForTracking:completion];
}

- (void)reportLocationInfo {
    // 防重复上报检查：5秒内不重复上报
    NSTimeInterval currentTime = [[NSDate date] timeIntervalSince1970];
    NSTimeInterval timeSinceLastReport = currentTime - self.lastReportTime;

    if (self.isReporting) {
        NSLog(@"🏠 [LocationManager] ⚠️ 地址埋点正在上报中，跳过重复请求");
        return;
    }

    if (timeSinceLastReport < 5.0 && self.lastReportTime > 0) {
        NSLog(@"🏠 [LocationManager] ⚠️ 距离上次上报仅%.1f秒，跳过重复上报（防重复间隔5秒）", timeSinceLastReport);
        return;
    }

    // 设置上报状态
    self.isReporting = YES;
    self.lastReportTime = currentTime;

    // 打印地址埋点请求开始标识和当前缓存坐标
    CLLocationCoordinate2D cachedCoord = [self getCachedCoordinate];
    NSLog(@"🏠 [LocationManager] ===== 地址埋点上报开始 =====");
    NSLog(@"🏠 [LocationManager] 请求前缓存坐标: %.6f, %.6f", cachedCoord.latitude, cachedCoord.longitude);
    NSLog(@"🏠 [LocationManager] 开始获取实时高精度定位...");

    [self getCurrentLocationForTracking:^(CLLocationCoordinate2D coordinate, NSError * _Nullable error) {
        if (error) {
            NSLog(@"🏠 [LocationManager] 位置信息上报 - 定位失败: %@", error.localizedDescription);
            // 即使定位失败，也尝试使用缓存坐标上报
            coordinate = self.cachedCoordinate;
        }

        if (!CLLocationCoordinate2DIsValid(coordinate)) {
            NSLog(@"🏠 [LocationManager] 位置信息上报 - 无有效坐标，跳过上报");
            // 重置上报状态
            self.isReporting = NO;
            return;
        }

        NSLog(@"🏠 [LocationManager] 位置信息上报 - 开始反向地理编码: %.6f, %.6f", coordinate.latitude, coordinate.longitude);

        // 使用反向地理编码获取详细地址信息
        CLGeocoder *geocoder = [[CLGeocoder alloc] init];
        CLLocation *location = [[CLLocation alloc] initWithLatitude:coordinate.latitude longitude:coordinate.longitude];

        [geocoder reverseGeocodeLocation:location completionHandler:^(NSArray<CLPlacemark *> * _Nullable placemarks, NSError * _Nullable geocodeError) {
            NSString *province = @"";     // 省
            NSString *city = @"";         // 市
            NSString *district = @"";     // 区
            NSString *street = @"";       // 街道
            NSString *country = @"China"; // 国家
            NSString *countryCode = @"CN"; // 国家代码

            if (geocodeError) {
                NSLog(@"[LocationManager] 反向地理编码失败: %@", geocodeError.localizedDescription);
            } else if (placemarks.count > 0) {
                CLPlacemark *placemark = placemarks.firstObject;
                NSLog(@"[LocationManager] 反向地理编码成功: %@", placemark);

                // 提取地址信息
                province = placemark.administrativeArea ?: @"";           // 省/州
                city = placemark.locality ?: placemark.subAdministrativeArea ?: @""; // 市
                district = placemark.subLocality ?: @"";                 // 区/县
                street = placemark.thoroughfare ?: placemark.name ?: @""; // 街道
                country = placemark.country ?: @"China";                 // 国家
                countryCode = placemark.ISOcountryCode ?: @"CN";         // 国家代码

                NSLog(@"[LocationManager] 地址解析结果 - 省:%@ 市:%@ 区:%@ 街道:%@", province, city, district, street);
            }

            // 构建上报参数
            NSDictionary *params = @{
                @"darrein": province,                    // 省
                @"italways": countryCode,                // 国家code
                @"unkindly": country,                    // 国家
                @"askedme": street,                      // 街道
                @"invitation": [NSString stringWithFormat:@"%.6f", coordinate.latitude],   // 纬度
                @"hersuch": [NSString stringWithFormat:@"%.6f", coordinate.longitude],     // 经度
                @"sharethe": city,                       // 市
                @"adoration": district                   // 区
            };

            // 专门的埋点测试日志 - 分行打印避免截断
            NSLog(@"[埋点测试] 地址埋点上报用到的经度是%@", params[@"hersuch"]);
            NSLog(@"[埋点测试] 地址埋点上报用到的纬度是%@", params[@"invitation"]);

            [NetworkManager postFormWithAPI:@"Alicia/cardboardcontainers" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable networkError) {
                // 重置上报状态
                self.isReporting = NO;

                if (networkError) {
                    NSLog(@"[埋点测试] 地址埋点上报失败: %@", networkError.localizedDescription);
                } else {
                    NSLog(@"[埋点测试] 地址埋点上报成功");
                }
            }];
        }];
    }];
}

#pragma mark - Private Methods

- (CLLocationCoordinate2D)loadCachedCoordinate {
    NSData *data = [[NSUserDefaults standardUserDefaults] objectForKey:kCachedCoordinateKey];
    if (data) {
        CLLocationCoordinate2D coordinate;
        [data getBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        if (CLLocationCoordinate2DIsValid(coordinate)) {
            return coordinate;
        }
    }
    // 返回无效坐标，表示没有缓存数据
    return kCLLocationCoordinate2DInvalid;
}

- (void)saveCachedCoordinate:(CLLocationCoordinate2D)coordinate {
    if (CLLocationCoordinate2DIsValid(coordinate)) {
        // 检查是否有显著变化（超过0.5米的移动）
        BOOL hasSignificantChange = NO;
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            CLLocation *oldLocation = [[CLLocation alloc] initWithLatitude:self.cachedCoordinate.latitude longitude:self.cachedCoordinate.longitude];
            CLLocation *newLocation = [[CLLocation alloc] initWithLatitude:coordinate.latitude longitude:coordinate.longitude];
            CLLocationDistance distance = [newLocation distanceFromLocation:oldLocation];
            hasSignificantChange = distance > 0.5; // 0.5米以上的移动才算显著变化

            NSLog(@"[LocationManager] 💾 坐标缓存更新 - 距离上次: %.2fm %@", distance, hasSignificantChange ? @"(显著变化)" : @"(微小变化)");
        } else {
            hasSignificantChange = YES;
            NSLog(@"[LocationManager] 💾 首次坐标缓存");
        }

        NSData *data = [NSData dataWithBytes:&coordinate length:sizeof(CLLocationCoordinate2D)];
        [[NSUserDefaults standardUserDefaults] setObject:data forKey:kCachedCoordinateKey];
        [[NSUserDefaults standardUserDefaults] synchronize];
        self.cachedCoordinate = coordinate;

        NSLog(@"[LocationManager] 💾 坐标已缓存: %.6f, %.6f", coordinate.latitude, coordinate.longitude);
    }
}

- (void)handleLocationTimeout:(LocationRequest *)request {
    if (!request || ![self.activeRequests containsObject:request]) {
        return;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];

    CLLocationCoordinate2D finalCoordinate;
    NSError *error = nil;

    // 优先使用本次定位过程中获得的最佳位置
    if (request.bestLocation && CLLocationCoordinate2DIsValid(request.bestLocation.coordinate)) {
        finalCoordinate = request.bestLocation.coordinate;
        [self saveCachedCoordinate:finalCoordinate];

        if (request.bestLocation.horizontalAccuracy > kAcceptableAccuracy) {
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: [NSString stringWithFormat:@"Timeout with low accuracy: %.1fm", request.bestLocation.horizontalAccuracy]}];
        }
    } else {
        // 使用缓存坐标兜底
        if (CLLocationCoordinate2DIsValid(self.cachedCoordinate)) {
            finalCoordinate = self.cachedCoordinate;
            error = [NSError errorWithDomain:@"LocationManager" code:-2 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, using cached coordinate"}];
        } else {
            finalCoordinate = kCLLocationCoordinate2DInvalid;
            error = [NSError errorWithDomain:@"LocationManager" code:-3 userInfo:@{NSLocalizedDescriptionKey: @"Location timeout, no valid coordinate available"}];
        }
    }

    // 回调结果
    if (request.completion) {
        request.completion(finalCoordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)cleanupRequest:(LocationRequest *)request {
    if (!request) {
        return;
    }

    // 取消定时器
    if (request.timeoutTimer) {
        [request.timeoutTimer invalidate];
        request.timeoutTimer = nil;
    }

    // 停止定位
    [request.locationManager stopUpdatingLocation];
    request.locationManager.delegate = nil;

    // 从活跃请求列表中移除
    [self.activeRequests removeObject:request];
}

- (LocationRequest *)findRequestForLocationManager:(CLLocationManager *)manager {
    for (LocationRequest *request in self.activeRequests) {
        if (request.locationManager == manager) {
            return request;
        }
    }
    return nil;
}

#pragma mark - CLLocationManagerDelegate

- (void)locationManager:(CLLocationManager *)manager didUpdateLocations:(NSArray<CLLocation *> *)locations {
    CLLocation *location = locations.lastObject;
    if (!location) {
        NSLog(@"[LocationManager] ❌ 收到空位置数据");
        return;
    }

    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        NSLog(@"[LocationManager] ❌ 找不到对应的定位请求");
        return;
    }

    // 检查位置数据的新鲜度
    NSTimeInterval locationAge = -[location.timestamp timeIntervalSinceNow];
    if (locationAge > kLocationMaxAge) {
        NSLog(@"[LocationManager] ⏰ 位置数据过旧 %.1fs，忽略", locationAge);
        return;
    }

    // 检查位置精度有效性
    if (location.horizontalAccuracy < 0) {
        NSLog(@"[LocationManager] ❌ 位置精度无效 %.1fm，忽略", location.horizontalAccuracy);
        return;
    }

    request.locationUpdateCount++;
    NSTimeInterval elapsedTime = [[NSDate date] timeIntervalSince1970] - request.startTime;

    NSLog(@"[LocationManager] 📍 收到位置更新 #%ld - 坐标: %.6f, %.6f, 精度: %.2fm, 数据年龄: %.1fs",
          (long)request.locationUpdateCount, location.coordinate.latitude, location.coordinate.longitude,
          location.horizontalAccuracy, locationAge);

    // 智能精度判断逻辑
    BOOL shouldAcceptLocation = [self shouldAcceptLocation:location forRequest:request elapsedTime:elapsedTime];

    if (!shouldAcceptLocation) {
        // 更新最佳位置记录
        if (!request.bestLocation || location.horizontalAccuracy < request.bestLocation.horizontalAccuracy) {
            NSLog(@"[LocationManager] 🔄 更新最佳位置记录: %.2fm -> %.2fm",
                  request.bestLocation ? request.bestLocation.horizontalAccuracy : 999.0, location.horizontalAccuracy);
            request.bestLocation = location;
        }
        return;
    }

    // 选择最终位置
    CLLocation *finalLocation = request.bestLocation && request.bestLocation.horizontalAccuracy < location.horizontalAccuracy ? request.bestLocation : location;

    NSLog(@"[LocationManager] ✅ 定位完成 - 最终坐标: %.6f, %.6f, 精度: %.2fm, 耗时: %.1fs",
          finalLocation.coordinate.latitude, finalLocation.coordinate.longitude,
          finalLocation.horizontalAccuracy, elapsedTime);

    // 保存到缓存
    [self saveCachedCoordinate:finalLocation.coordinate];

    // 回调成功结果
    if (request.completion) {
        request.completion(finalLocation.coordinate, nil);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didFailWithError:(NSError *)error {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    // 使用缓存坐标回调
    if (request.completion) {
        CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
        request.completion(coordinate, error);
    }

    // 清理请求
    [self cleanupRequest:request];
}

- (void)locationManager:(CLLocationManager *)manager didChangeAuthorizationStatus:(CLAuthorizationStatus)status {
    LocationRequest *request = [self findRequestForLocationManager:manager];
    if (!request) {
        return;
    }

    switch (status) {
        case kCLAuthorizationStatusAuthorizedWhenInUse:
        case kCLAuthorizationStatusAuthorizedAlways:
            [manager startUpdatingLocation];
            break;
        case kCLAuthorizationStatusDenied:
        case kCLAuthorizationStatusRestricted:
            if (request.completion) {
                CLLocationCoordinate2D coordinate = CLLocationCoordinate2DIsValid(self.cachedCoordinate) ? self.cachedCoordinate : kCLLocationCoordinate2DInvalid;
                request.completion(coordinate, [NSError errorWithDomain:@"LocationManager" code:-1 userInfo:@{NSLocalizedDescriptionKey: @"Location permission denied"}]);
            }
            [self cleanupRequest:request];
            break;
        case kCLAuthorizationStatusNotDetermined:
            break;
        default:
            break;
    }
}

#pragma mark - Helper Methods

/// 智能判断是否应该接受当前位置 - 针对高精度实时定位优化
- (BOOL)shouldAcceptLocation:(CLLocation *)location forRequest:(LocationRequest *)request elapsedTime:(NSTimeInterval)elapsedTime {
    // 第一次收到位置
    if (!request.hasReceivedFirstLocation) {
        request.hasReceivedFirstLocation = YES;
    }

    NSLog(@"[LocationManager] 定位精度评估 - 当前精度: %.2fm, 已等待: %.1fs, 更新次数: %ld",
          location.horizontalAccuracy, elapsedTime, (long)request.locationUpdateCount);

    // 立即接受的条件：亚米级精度（0.5米以内）
    if (location.horizontalAccuracy <= kBestAccuracy) {
        NSLog(@"[LocationManager] ✅ 达到最佳精度 %.2fm，立即接受", location.horizontalAccuracy);
        return YES;
    }

    // 良好精度 + 等待时间足够 + 收到多次更新
    if (location.horizontalAccuracy <= kGoodAccuracy && elapsedTime >= kMinWaitTime && request.locationUpdateCount >= 3) {
        NSLog(@"[LocationManager] ✅ 达到良好精度 %.2fm，等待时间充足，接受", location.horizontalAccuracy);
        return YES;
    }

    // 可接受精度 + 等待时间较长 + 收到足够更新
    if (location.horizontalAccuracy <= kAcceptableAccuracy && elapsedTime >= (kMinWaitTime * 1.5) && request.locationUpdateCount >= 5) {
        NSLog(@"[LocationManager] ⚠️ 达到可接受精度 %.2fm，等待时间较长，接受", location.horizontalAccuracy);
        return YES;
    }

    // 超过85%超时时间，使用最佳可用位置
    if (elapsedTime >= (kLocationTimeout * 0.85)) {
        NSLog(@"[LocationManager] ⏰ 接近超时，使用当前最佳位置 %.2fm", location.horizontalAccuracy);
        return YES;
    }

    // 继续等待更好的精度
    NSLog(@"[LocationManager] ⏳ 继续等待更高精度，当前 %.2fm", location.horizontalAccuracy);
    return NO;
}



@end
