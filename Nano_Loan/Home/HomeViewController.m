#import "HomeViewController.h"
#import "NetworkManager.h"
#import "HUD.h"
#import <SafariServices/SafariServices.h>
#import "ProductsCertificationViewController.h"
#import "H5WebViewController.h"
#import "LocationManager.h"
#import <CoreLocation/CoreLocation.h>
#import <AdSupport/ASIdentifierManager.h>
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import "DeviceIDManager.h"
#import "AppDelegate.h"
#import "DeviceInfoManager.h"
#import <Masonry/Masonry.h>
#import "LoginViewController.h"
#import "HomeProductCell.h"
#import <MJRefresh/MJRefresh.h>

@interface HomeViewController () <UITableViewDataSource, UITableViewDelegate>

// 用于区分首页样式: YES => 大卡样式, NO => 小卡样式
@property (nonatomic, assign) BOOL isLargeCardStyle;

// 大卡样式下的产品信息（darning 字段字典）
@property (nonatomic, strong) NSDictionary *largeCardProductInfo;

// 小卡样式下的产品数据源, 第 0 个为顶部主卡, 其余为列表数据
@property (nonatomic, strong) NSArray<NSDictionary *> *smallCardProducts;

// 小卡上方主产品
@property (nonatomic, strong) NSDictionary *smallCardTopProduct;

// 小卡列表其余产品
@property (nonatomic, strong) NSArray<NSDictionary *> *smallCardListProducts;

// 客服H5链接（来自 mayonnaise.cardboardcontainers）
@property (nonatomic, copy) NSString *customerServiceURL;
// 协议链接（来自 pickles.eating）
@property (nonatomic, copy) NSString *agreementURL;

@property (nonatomic, strong) UITableView *productsTableView;

// 大卡样式下的滚动容器
@property (nonatomic, strong) UIScrollView *mainScrollView;
// 新增：下拉刷新控件（小卡样式使用）
@property (nonatomic, strong) UIRefreshControl *refreshControl;
// 新增：MJRefresh下拉刷新控件（大卡样式使用）
@property (nonatomic, strong) MJRefreshNormalHeader *mjRefreshHeader;

@end

@implementation HomeViewController

// 移除此前样式遗留的所有内容视图，避免视图切换后重叠
- (void)clearContentViews {
    // 保留 HUD 等可能在 window 上的视图，仅清理 self.view 子视图
    for (UIView *sub in self.view.subviews.copy) {
        [sub removeFromSuperview];
    }
    // 同时将引用置空，避免悬挂指针
    self.mainScrollView = nil;
    self.productsTableView = nil;
    self.mjRefreshHeader = nil;
}

- (void)viewDidLoad {
    [super viewDidLoad];
    NSLog(@"[HomeVC] viewDidLoad - 开始初始化");
    // 只初始化小卡样式的刷新控件，大卡样式的在buildLargeCardUI中初始化
    [self setupSmallCardRefreshControl];
    [self setupNotificationObservers];
    [self fetchHomeData];
}

- (void)viewDidAppear:(BOOL)animated {
    [super viewDidAppear:animated];

    // 确保清理可能残留的HUD
    UIView *hudSuperView = self.view.window ?: self.view;
    [HUD hideForView:hudSuperView];

    // 进入首页时上报位置信息、设备标识符和设备信息，然后获取数据
    NSLog(@"[HomeVC] viewDidAppear - 开始上报位置信息、IDFV & IDFA、设备信息");
    [[LocationManager sharedManager] reportLocationInfo];
    [self reportDeviceIdentifiers];
    [self reportDeviceInfo];
    [self fetchHomeData];
    [self showLocationGuideIfNeeded];
}

- (void)viewWillAppear:(BOOL)animated {
    [super viewWillAppear:animated];
    self.navigationController.navigationBarHidden = YES;
}

- (void)viewWillDisappear:(BOOL)animated {
    [super viewWillDisappear:animated];
    self.navigationController.navigationBarHidden = NO;

    // 清理可能残留的HUD
    UIView *hudSuperView = self.view.window ?: self.view;
    [HUD hideForView:hudSuperView];
}

- (void)dealloc {
    [[NSNotificationCenter defaultCenter] removeObserver:self];
}

#pragma mark - 数据刷新
- (void)refreshData {
    NSLog(@"[HomeVC] refreshData 被调用 - 当前样式: %@", self.isLargeCardStyle ? @"大卡" : @"小卡");
    if (self.isLargeCardStyle) {
        NSLog(@"[HomeVC] 大卡样式下拉刷新，MJRefresh状态: %@", self.mjRefreshHeader.isRefreshing ? @"刷新中" : @"未刷新");
    } else {
        NSLog(@"[HomeVC] 小卡样式下拉刷新，UIRefreshControl状态: %@", self.refreshControl.isRefreshing ? @"刷新中" : @"未刷新");
    }

    // 下拉刷新时上报位置信息、设备标识符和设备信息
    NSLog(@"[HomeVC] 下拉刷新 - 开始上报位置信息、IDFV & IDFA、设备信息");
    [[LocationManager sharedManager] reportLocationInfo];
    [self reportDeviceIdentifiers];
    [self reportDeviceInfo];

    // 调用现有的数据加载方法
    [self fetchHomeData];
}

// 解析首页元素类型
- (void)parseHomeElements:(NSArray *)elements {
    NSLog(@"开始解析首页元素，共 %lu 个元素", (unsigned long)elements.count);
    // 重置数据
    self.largeCardProductInfo = nil;
    self.smallCardProducts = nil;
    self.smallCardTopProduct = nil;
    self.smallCardListProducts = nil;
    self.isLargeCardStyle = NO;

    NSMutableArray *tmpList = [NSMutableArray array];
    NSDictionary *headerProduct = nil;

    for (NSDictionary *item in elements) {
        if (![item isKindOfClass:[NSDictionary class]]) {
            continue;
        }

        NSString *subject = item[@"subject"];
        NSArray *darning = item[@"darning"];
        if (![darning isKindOfClass:[NSArray class]] || darning.count == 0) {
            continue;
        }

        NSDictionary *firstDarn = darning.firstObject;

        // 调试日志
        NSString *originalType = [self getOriginalElementType:subject];
        NSLog(@"混淆值: %@ -> 原始类型: %@", subject, originalType);
        NSLog(@"元素内容: %@", firstDarn);

        if ([subject isEqualToString:@"zelle"]) {
            // 大卡样式
            self.isLargeCardStyle = YES;
            self.largeCardProductInfo = firstDarn;
            break;
        } else if ([subject isEqualToString:@"Rougier"]) {
            // 视为顶部主产品
            headerProduct = firstDarn;
        } else if ([subject isEqualToString:@"beginning"]) {
            // 列表产品：添加该 subject 下所有 darning 项，而非仅首个
            for (NSDictionary *darnItem in darning) {
                if ([darnItem isKindOfClass:[NSDictionary class]]) {
                    [tmpList addObject:darnItem];
                }
            }
        }
    }

    if (!self.isLargeCardStyle) {
        // 如果未拿到 Rougier，则默认第一个列表产品为 header
        if (!headerProduct && tmpList.count > 0) {
            headerProduct = tmpList.firstObject;
            [tmpList removeObjectAtIndex:0];
        }

        self.smallCardTopProduct = headerProduct;
        self.smallCardListProducts = [tmpList copy];

        // 构建完整数组（顶部 + 列表）供其他逻辑使用
        NSMutableArray *allProducts = [NSMutableArray array];
        if (headerProduct) {
            [allProducts addObject:headerProduct];
        }
        [allProducts addObjectsFromArray:tmpList];
        self.smallCardProducts = [allProducts copy];
    }

    dispatch_async(dispatch_get_main_queue(), ^{
        [self buildCorrespondingUI];
    });
}

#pragma mark - UI 构建

- (void)buildCorrespondingUI {
    // 公共背景色
    self.view.backgroundColor = [UIColor colorWithRed:131.0/255.0 green:178.0/255.0 blue:1 alpha:1];

    // 彻底移除上一次构建的内容，防止不同样式元素残留
    [self clearContentViews];

    if (self.isLargeCardStyle) {
        NSLog(@"[HomeVC] 构建大卡样式UI");
        // 创建滚动容器
        self.mainScrollView = [[UIScrollView alloc] initWithFrame:self.view.bounds];
        self.mainScrollView.alwaysBounceVertical = YES;
        self.mainScrollView.showsVerticalScrollIndicator = NO;
        self.mainScrollView.autoresizingMask = UIViewAutoresizingFlexibleWidth | UIViewAutoresizingFlexibleHeight;
        [self.view addSubview:self.mainScrollView];
        NSLog(@"[HomeVC] mainScrollView 创建完成，frame: %@", NSStringFromCGRect(self.mainScrollView.frame));

        // 初始化并绑定大卡样式的下拉刷新控件
        [self setupLargeCardRefreshControl];

        // 立即绑定MJRefresh到ScrollView
        if (self.mjRefreshHeader) {
            NSLog(@"[HomeVC] 立即绑定 MJRefreshHeader 到新创建的 mainScrollView");
            self.mainScrollView.mj_header = self.mjRefreshHeader;
            NSLog(@"[HomeVC] 绑定结果: %@", self.mainScrollView.mj_header ? @"成功" : @"失败");
        }

        [self buildLargeCardUI];
    } else {
        NSLog(@"[HomeVC] 构建小卡样式UI");
        [self buildSmallCardUI];
    }

    // 重新挂载下拉刷新控件
    [self attachRefreshControl];
}

// 大卡 UI
- (void)buildLargeCardUI {
    CGFloat width = self.view.bounds.size.width;
    CGFloat safeTop = self.view.safeAreaInsets.top; // 顶部安全区

    CGFloat margin = 14.0;

    /***** 1. 顶部背景图 *****/
    CGFloat topBgAspect = 222.0f / 375.0f; // 高 / 宽
    CGFloat topBgHeight = width * topBgAspect;
    UIImageView *topBgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, width, topBgHeight)];
    topBgView.image = [UIImage imageNamed:@"home_top_bg"]; // TODO: 替换为实际素材名
    topBgView.contentMode = UIViewContentModeScaleAspectFill;
    topBgView.clipsToBounds = YES;
    [self.view insertSubview:topBgView belowSubview:self.mainScrollView];

    // 绑定MJRefresh下拉刷新控件到滚动容器（大卡样式）
    if (self.mjRefreshHeader && self.mainScrollView) {
        NSLog(@"[HomeVC] 绑定 MJRefreshHeader 到 mainScrollView");
        self.mainScrollView.mj_header = self.mjRefreshHeader;
        NSLog(@"[HomeVC] MJRefreshHeader 绑定完成，mj_header: %@", self.mainScrollView.mj_header);
    } else {
        NSLog(@"[HomeVC] 绑定失败 - mjRefreshHeader: %@, mainScrollView: %@", self.mjRefreshHeader, self.mainScrollView);
    }

    /***** 2. 产品卡片背景 *****/
    CGFloat cardWidth = width - 2 * margin; // 347pt when width=375
    CGFloat cardAspect = 228.0f / 347.0f;
    CGFloat cardHeight = cardWidth * cardAspect;

    // 让卡片与顶部背景稍微有重叠效果（可按需调整 offset）
    CGFloat cardY = CGRectGetMaxY(topBgView.frame) - cardHeight * 0.2; // overlap 40%
    UIImageView *cardBgView = [[UIImageView alloc] initWithFrame:CGRectMake(margin, cardY, cardWidth, cardHeight)];
    cardBgView.image = [UIImage imageNamed:@"home_product_bg"];
    cardBgView.contentMode = UIViewContentModeScaleAspectFill;
    cardBgView.clipsToBounds = YES;
    // 允许卡片内部子视图接收触控事件
    cardBgView.userInteractionEnabled = YES;
    cardBgView.layer.cornerRadius = 12;
    cardBgView.layer.masksToBounds = YES;
    [self.mainScrollView addSubview:cardBgView];

    // 添加 Logo
    CGFloat logoSize = 42.0f;
    UIImageView *logoView = [[UIImageView alloc] initWithFrame:CGRectMake(12.0f, 6.0f, logoSize, logoSize)];
    // 设置默认占位图
    logoView.image = [UIImage imageNamed:@"home_product_logo"];
    logoView.contentMode = UIViewContentModeScaleAspectFill;
    logoView.clipsToBounds = YES;
    [cardBgView addSubview:logoView];

    // 根据接口返回的图片地址异步加载替换，占位图仍为本地资源
    NSString *logoURLString = self.largeCardProductInfo[@"tapestrywork"];
    if ([logoURLString isKindOfClass:[NSString class]] && logoURLString.length > 0) {
        NSURL *logoURL = [NSURL URLWithString:logoURLString];
        if (logoURL) {
            __weak typeof(logoView) weakLogoView = logoView;
            NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:logoURL completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                if (!error && data.length > 0) {
                    UIImage *downloadedImage = [UIImage imageWithData:data];
                    if (downloadedImage) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            if (weakLogoView) {
                                weakLogoView.image = downloadedImage;
                                NSLog(@"[HomeVC] 大卡样式 Logo 网络图片加载成功: %@", logoURLString);
                            }
                        });
                    }
                } else {
                    NSLog(@"[HomeVC] 大卡样式 Logo 网络图片加载失败: %@, error: %@", logoURLString, error);
                }
            }];
            [task resume];
        }
    } else {
        NSLog(@"[HomeVC] 大卡样式 tapestrywork 参数为空或无效，使用默认占位图");
    }

    // 产品名称，位于 logo 右侧 2pt
    CGFloat nameX = CGRectGetMaxX(logoView.frame) + 4.0f;
    CGFloat nameWidth = cardWidth - nameX - 10.0f;
    UILabel *productNameLabel = [[UILabel alloc] initWithFrame:CGRectMake(nameX, 0, nameWidth, 18.0f)];
    productNameLabel.font = [UIFont systemFontOfSize:14.0f];
    productNameLabel.textColor = [UIColor colorWithRed:4/255.0 green:9/255.0 blue:2/255.0 alpha:1];
    productNameLabel.text = self.largeCardProductInfo[@"cando"] ?: @"--";
    // 垂直居中于 logo
    productNameLabel.center = CGPointMake(productNameLabel.center.x, CGRectGetMidY(logoView.frame));
    [cardBgView addSubview:productNameLabel];

    // 金额标签，距卡片顶部 49pt，高度 66pt，居中
    UILabel *amountLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 49.0f, cardWidth, 66.0f)];
    amountLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:56.0f];
    amountLabel.textColor = [UIColor colorWithRed:4/255.0 green:9/255.0 blue:2/255.0 alpha:1];
    amountLabel.textAlignment = NSTextAlignmentCenter;
    amountLabel.adjustsFontSizeToFitWidth = YES;
    amountLabel.text = self.largeCardProductInfo[@"disappointment"] ?: @"--";
    [cardBgView addSubview:amountLabel];

    /***** 底部展示图片 *****/
    UIImageView *displayImg1 = [[UIImageView alloc] init];
    displayImg1.image = [UIImage imageNamed:@"home_card_pic1"];// TODO: 替换实际资源
    displayImg1.contentMode = UIViewContentModeScaleAspectFill;
    displayImg1.clipsToBounds = YES;
    [cardBgView addSubview:displayImg1];

    UIImageView *displayImg2 = [[UIImageView alloc] init];
    displayImg2.image = [UIImage imageNamed:@"home_card_pic2"];// TODO: 替换实际资源
    displayImg2.contentMode = UIViewContentModeScaleAspectFill;
    displayImg2.clipsToBounds = YES;
    [cardBgView addSubview:displayImg2];

    // 开启交互并添加点击手势，用于调试打印
    displayImg1.userInteractionEnabled = YES;
    displayImg2.userInteractionEnabled = YES;

    UITapGestureRecognizer *tap1 = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(leftDisplayImageTapped)];
    [displayImg1 addGestureRecognizer:tap1];

    UITapGestureRecognizer *tap2 = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(rightDisplayImageTapped)];
    [displayImg2 addGestureRecognizer:tap2];

    CGFloat displayWidthRatio = 152.0/347.0; // 相对于卡片宽度

    // 更新约束：两图水平排列，距卡片底 20pt；左图左 15pt，右图右 15pt，中间间隔 10pt
    [displayImg1 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cardBgView).offset(15.0f);
        make.bottom.equalTo(cardBgView).offset(-20.0f);
        make.width.equalTo(cardBgView.mas_width).multipliedBy(displayWidthRatio);
        make.height.equalTo(displayImg1.mas_width).multipliedBy(69.0/152.0);
    }];

    [displayImg2 mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(displayImg1.mas_right).offset(10.0f);
        make.right.equalTo(cardBgView).offset(-15.0f);
        make.centerY.equalTo(displayImg1);
        make.width.equalTo(displayImg1);
        make.height.equalTo(displayImg1);
    }];

    // 触发布局以确保约束生效
    [cardBgView layoutIfNeeded];

    // 顶部装饰 Logo
    UIImageView *decorLogo = [[UIImageView alloc] init];
    decorLogo.image = [UIImage imageNamed:@"home_card_decor_logo"]; // TODO: 替换实际资源
    decorLogo.contentMode = UIViewContentModeScaleAspectFill;
    decorLogo.clipsToBounds = YES;
    [self.mainScrollView insertSubview:decorLogo belowSubview:cardBgView];

    CGFloat decorWidthRatio = 118.0/347.0; // 以卡片宽度为基准

    [decorLogo mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(cardBgView.mas_left).offset(12.0f);
        make.width.equalTo(cardBgView.mas_width).multipliedBy(decorWidthRatio);
        make.height.equalTo(decorLogo.mas_width).multipliedBy(83.0/118.0);
        make.bottom.equalTo(cardBgView.mas_top).offset(24.0f);
    }];

    // 重新布局，更新 contentSize 时可用 cardBgView frame; decorLogo 不影响 yOffset
    [self.mainScrollView layoutIfNeeded];

    CGFloat yOffset = 0.0f; // 用于后续视图的 Y 起点

    if (self.agreementURL.length > 0) {
        // 使用 Masonry 约束协议按钮图片
        CGFloat sidePaddingProtocol = 50.0f;
        UIImageView *protocolImgView = [[UIImageView alloc] init];
        protocolImgView.image = [UIImage imageNamed:@"home_protocol_bg"];
        protocolImgView.userInteractionEnabled = YES;
        UITapGestureRecognizer *tapProtocol = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(protocolImageTapped)];
        [protocolImgView addGestureRecognizer:tapProtocol];
        [self.mainScrollView addSubview:protocolImgView];

        [protocolImgView mas_makeConstraints:^(MASConstraintMaker *make) {
            make.left.equalTo(self.mainScrollView).offset(sidePaddingProtocol);
            make.right.equalTo(self.mainScrollView).offset(-sidePaddingProtocol);
            make.top.equalTo(cardBgView.mas_bottom).offset(15.0f);
            make.height.equalTo(protocolImgView.mas_width).multipliedBy(17.0/276.0);
        }];

        // 再次 layout 获取新的 yOffset
        [self.mainScrollView layoutIfNeeded];
        yOffset = CGRectGetMaxY(protocolImgView.frame) + 15.0f;
    } else {
        // 无协议链接，直接以下一个元素计算 Y
        yOffset = CGRectGetMaxY(cardBgView.frame) + 15.0f;
    }

    /***** 4. 申请按钮背景图 *****/
    CGFloat applyHeight = 50.0f;
    UIButton *applyButton = [UIButton buttonWithType:UIButtonTypeCustom];
    applyButton.frame = CGRectMake(margin, yOffset, cardWidth, applyHeight);
    UIImage *applyBgImg = [UIImage imageNamed:@"home_apply_bg"]; // TODO: 替换为实际素材名
    [applyButton setBackgroundImage:applyBgImg forState:UIControlStateNormal];
    NSString *applyTitle = self.largeCardProductInfo[@"embroidery"] ?: @"Apply";
    [applyButton setTitle:applyTitle forState:UIControlStateNormal];
    [applyButton setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    applyButton.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:22];
    applyButton.layer.cornerRadius = 25;
    applyButton.layer.masksToBounds = YES;
    [applyButton addTarget:self action:@selector(handleApplyButton:) forControlEvents:UIControlEventTouchUpInside];
    [self.mainScrollView addSubview:applyButton];

    yOffset = CGRectGetMaxY(applyButton.frame) + 20.0f;

    /***** 5. 继续后续区域（linkAreaView & promoView） *****/
    // 3. 链接区域背景图（保持 347:184 等比缩放，左右留 14pt）
    CGFloat linkWidth = cardWidth;
    CGFloat linkAspect = 184.0f / 347.0f; // 高 / 宽
    CGFloat linkHeight = linkWidth * linkAspect;

    UIImageView *linkAreaView = [[UIImageView alloc] initWithFrame:CGRectMake(margin, yOffset, linkWidth, linkHeight)];
    linkAreaView.contentMode = UIViewContentModeScaleAspectFill;
    linkAreaView.clipsToBounds = YES;
    linkAreaView.image = [UIImage imageNamed:@"home_link_bg"]; // TODO: 替换为实际素材名
    linkAreaView.layer.cornerRadius = 12;
    linkAreaView.layer.masksToBounds = YES;
    [self.mainScrollView addSubview:linkAreaView];

    yOffset = CGRectGetMaxY(linkAreaView.frame) + 15.0f;

    // 4. 宣传背景图区域（保持等比缩放，左右留 14pt 边距）
    CGFloat promoWidth = cardWidth;
    CGFloat promoAspect = 163.5f / 347.0f;
    CGFloat promoHeight = promoWidth * promoAspect + 4.0f; // 额外 +4pt 用于阴影

    UIImageView *promoView = [[UIImageView alloc] initWithFrame:CGRectMake(margin, yOffset, promoWidth, promoHeight)];
    promoView.contentMode = UIViewContentModeScaleAspectFill;
    promoView.clipsToBounds = YES;
    promoView.image = [UIImage imageNamed:@"home_promo_bg"];
    promoView.layer.cornerRadius = 12;
    promoView.layer.masksToBounds = YES;
    [self.mainScrollView addSubview:promoView];

    // 设置滚动区域（额外 +90pt，避免被底部 TabBar 遮挡）
    CGFloat extraPadding = 90.0f;
    CGFloat contentHeight = CGRectGetMaxY(promoView.frame) + extraPadding;
    self.mainScrollView.contentSize = CGSizeMake(width, contentHeight);

    NSLog(@"[HomeVC] 大卡样式ScrollView设置完成:");
    NSLog(@"[HomeVC] - frame: %@", NSStringFromCGRect(self.mainScrollView.frame));
    NSLog(@"[HomeVC] - contentSize: %@", NSStringFromCGSize(self.mainScrollView.contentSize));
    NSLog(@"[HomeVC] - alwaysBounceVertical: %@", self.mainScrollView.alwaysBounceVertical ? @"YES" : @"NO");
    NSLog(@"[HomeVC] - mj_header: %@", self.mainScrollView.mj_header);

    // 确保MJRefresh正确绑定
    if (self.mainScrollView.mj_header) {
        NSLog(@"[HomeVC] MJRefresh绑定成功");
    } else {
        NSLog(@"[HomeVC] ⚠️ MJRefresh绑定失败，尝试重新绑定");
        if (self.mjRefreshHeader) {
            self.mainScrollView.mj_header = self.mjRefreshHeader;
            NSLog(@"[HomeVC] 重新绑定结果: %@", self.mainScrollView.mj_header ? @"成功" : @"失败");
        }
    }

    // 添加测试手势，用于调试
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(2.0 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
        NSLog(@"[HomeVC] 2秒后检查MJRefresh状态: %@", self.mainScrollView.mj_header);
        if (self.mainScrollView.mj_header) {
            NSLog(@"[HomeVC] 可以尝试手动下拉测试刷新功能");
            NSLog(@"[HomeVC] 当前使用自动透明度模式，不下拉时完全隐藏");
        }
    });
}

// 小卡 UI
- (void)buildSmallCardUI {
    CGFloat width = self.view.bounds.size.width;
    CGFloat safeTop = self.view.safeAreaInsets.top;

    if (!self.smallCardTopProduct) {
        UILabel *emptyLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, safeTop + 40, width, 30)];
        emptyLabel.textAlignment = NSTextAlignmentCenter;
        emptyLabel.text = @"No products";
        [self.view addSubview:emptyLabel];
        return;
    }

    /***** 1. 顶部背景图 *****/
    CGFloat bgHeight = width * (192.0f / 375.0f); // 设计稿 375 宽对应 192 高，等比缩放
    UIImageView *topBgView = [[UIImageView alloc] initWithFrame:CGRectMake(0, 0, width, bgHeight)];
    topBgView.image = [UIImage imageNamed:@"home_top_bg_x"];
    topBgView.contentMode = UIViewContentModeScaleAspectFill;
    topBgView.clipsToBounds = YES;
    // 启用用户交互，允许点击背景图触发申请事件
    topBgView.userInteractionEnabled = YES;
    [self.view addSubview:topBgView];

    // 给背景图添加点击手势，点击整个背景图范围都可以触发申请
    UITapGestureRecognizer *topBgTap = [[UITapGestureRecognizer alloc] initWithTarget:self action:@selector(topApplyTapped)];
    [topBgView addGestureRecognizer:topBgTap];
    NSLog(@"[HomeVC] 小卡样式顶部背景图添加点击手势，整个背景图范围都可触发申请");

    NSDictionary *topProduct = self.smallCardTopProduct;

    //顶部产品价格
    UILabel *priceLabel = [[UILabel alloc] initWithFrame:CGRectMake(0, 0, width, 30)];
    priceLabel.text = topProduct[@"disappointment"] ?: @"0";
    priceLabel.textAlignment = NSTextAlignmentCenter;
    priceLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:30.0f];
    priceLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
    [self.view addSubview:priceLabel];
    [priceLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(36);
        make.height.mas_equalTo(37);
        make.bottom.equalTo(topBgView.mas_bottom).offset(-40);
    }];

    //广告词-固定
    UILabel *adLabel = [[UILabel alloc] init];
    adLabel.text = topProduct[@"stitches"] ?: @"Maximum limited";
    adLabel.textAlignment = NSTextAlignmentCenter;
    adLabel.font = [UIFont systemFontOfSize:13.0f];
    adLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
    [self.view addSubview:adLabel];
    [adLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.left.equalTo(self.view).offset(36);
        make.height.mas_equalTo(13);
        make.bottom.equalTo(priceLabel.mas_top).offset(-9);
    }];

    //右侧 logo
    UIImageView *rightLogoImgView = [[UIImageView alloc] init];
    // 默认占位图
    rightLogoImgView.image = [UIImage imageNamed:@"home_product_logo_x_r"];
    rightLogoImgView.contentMode = UIViewContentModeScaleAspectFill;
    rightLogoImgView.clipsToBounds = YES;
    [self.view addSubview:rightLogoImgView];
    [rightLogoImgView mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(self.view).offset(-32);
        make.width.mas_equalTo(30);
        make.height.mas_equalTo(30);
        make.centerY.equalTo(adLabel.mas_top);
    }];

    // 根据接口返回的图片地址异步加载替换，占位图仍为本地资源
    NSString *logoURLString = topProduct[@"tapestrywork"];
    if ([logoURLString isKindOfClass:[NSString class]] && logoURLString.length > 0) {
        NSURL *logoURL = [NSURL URLWithString:logoURLString];
        if (logoURL) {
            __weak typeof(rightLogoImgView) weakLogoView = rightLogoImgView;
            NSURLSessionDataTask *task = [[NSURLSession sharedSession] dataTaskWithURL:logoURL completionHandler:^(NSData * _Nullable data, NSURLResponse * _Nullable response, NSError * _Nullable error) {
                if (!error && data.length > 0) {
                    UIImage *downloadedImage = [UIImage imageWithData:data];
                    if (downloadedImage) {
                        dispatch_async(dispatch_get_main_queue(), ^{
                            if (weakLogoView) {
                                weakLogoView.image = downloadedImage;
                            }
                        });
                    }
                }
            }];
            [task resume];
        }
    }

    //产品名称
    UILabel *productNameLabel = [[UILabel alloc] init];
    productNameLabel.text = topProduct[@"cando"] ?: @"Product Name";
    productNameLabel.textAlignment = NSTextAlignmentCenter;
    productNameLabel.font = [UIFont systemFontOfSize:12.0f];
    productNameLabel.textColor = [UIColor colorWithRed:0x29/255.0 green:0x29/255.0 blue:0x29/255.0 alpha:1.0];
    [self.view addSubview:productNameLabel];
    [productNameLabel mas_makeConstraints:^(MASConstraintMaker *make) {
        make.right.equalTo(rightLogoImgView.mas_right);
        make.height.mas_equalTo(13);
        make.centerY.equalTo(priceLabel);
    }];

    /***** 2. 申请按钮 *****/
    CGFloat btnMarginX = 14.0f;
    CGFloat btnHeight = 50.0f;
    CGFloat btnY = CGRectGetMaxY(topBgView.frame) + 12.0f;

    UIButton *applyBtn = [UIButton buttonWithType:UIButtonTypeCustom];
    applyBtn.frame = CGRectMake(btnMarginX, btnY, width - 2 * btnMarginX, btnHeight);
    [applyBtn setBackgroundImage:[UIImage imageNamed:@"home_apply_bg"] forState:UIControlStateNormal];
    NSString *applyTitle = topProduct[@"embroidery"] ?: @"Apply";
    [applyBtn setTitle:applyTitle forState:UIControlStateNormal];
    [applyBtn setTitleColor:[UIColor whiteColor] forState:UIControlStateNormal];
    applyBtn.titleLabel.font = [UIFont fontWithName:@"Verdana-BoldItalic" size:22.0f];
    applyBtn.tag = 2000; // 顶部申请按钮标签
    [applyBtn addTarget:self action:@selector(topApplyTapped) forControlEvents:UIControlEventTouchUpInside];
    [self.view addSubview:applyBtn];

    /***** 3. Logo 图片 *****/
    CGFloat logoDesignWidth = 183.0f;
    CGFloat logoDesignHeight = 32.0f;
    CGFloat logoWidth = width * (logoDesignWidth / 375.0f); // 等比缩放
    CGFloat logoHeight = logoWidth * (logoDesignHeight / logoDesignWidth);
    CGFloat logoY = CGRectGetMaxY(applyBtn.frame) + 4.0f;

    UIImageView *logoImgView = [[UIImageView alloc] initWithFrame:CGRectMake((width - logoWidth) / 2.0f, logoY, logoWidth, logoHeight)];
    logoImgView.image = [UIImage imageNamed:@"home_product_logo_x"];
    logoImgView.contentMode = UIViewContentModeScaleAspectFill;
    logoImgView.clipsToBounds = YES;
    [self.view addSubview:logoImgView];

    /***** 4. 产品列表 TableView *****/
    CGFloat tableY = CGRectGetMaxY(logoImgView.frame) + 14.0f; // 下移 14pt
    self.productsTableView = [[UITableView alloc] initWithFrame:CGRectMake(0, tableY, width, self.view.bounds.size.height - tableY) style:UITableViewStyleGrouped];
    self.productsTableView.dataSource = self;
    self.productsTableView.delegate = self;
    // 允许在内容不足时也能下拉刷新
    self.productsTableView.alwaysBounceVertical = YES;
    self.productsTableView.showsVerticalScrollIndicator = NO;
    self.productsTableView.backgroundColor = [UIColor clearColor];
    self.productsTableView.separatorStyle = UITableViewCellSeparatorStyleNone;

    // 注册自定义 cell
    [self.productsTableView registerClass:[HomeProductCell class] forCellReuseIdentifier:@"HomeProductCell"];

    // 避免被 TabBar 遮挡，增加底部 contentInset
    CGFloat bottomInset = 80.0f; // 与需求一致
    self.productsTableView.contentInset = UIEdgeInsetsMake(0, 0, bottomInset, 0);
    self.productsTableView.scrollIndicatorInsets = self.productsTableView.contentInset;
    [self.view addSubview:self.productsTableView];

    // 确保下拉刷新控件绑定到列表；若之前使用于其他滚动视图，则重新创建
    if (self.refreshControl && self.refreshControl.superview == self.productsTableView) {
        // 已经正确绑定
    } else {
        // 重新创建并绑定新的 UIRefreshControl
        self.refreshControl = [[UIRefreshControl alloc] init];
        self.refreshControl.tintColor = [UIColor systemGrayColor];
        [self.refreshControl addTarget:self action:@selector(refreshData) forControlEvents:UIControlEventValueChanged];
        if (@available(iOS 10.0, *)) {
            self.productsTableView.refreshControl = self.refreshControl;
        } else {
            [self.productsTableView addSubview:self.refreshControl];
        }
    }
}

#pragma mark - Button Action

- (void)handleApplyButton:(UIButton *)sender {
    NSDictionary *productInfo = nil;
    if (self.isLargeCardStyle) {
        productInfo = self.largeCardProductInfo;
    } else {
        if (sender.tag == 2000) {
            // 顶部大卡申请按钮
            productInfo = self.smallCardProducts.firstObject;
        } else {
            NSInteger index = sender.tag - 3000; // 小卡列表按钮 tag 从 3000 开始
            if (index >= 0 && index < self.smallCardProducts.count) {
                productInfo = self.smallCardProducts[index];
            }
        }
    }

    if (!productInfo) return;

    // 产品准入 API (预留)
    [self precheckAndNavigateWithProduct:productInfo];
}

- (UIButton *)quickButtonWithTitle:(NSString *)title frame:(CGRect)frame tag:(NSInteger)tag {
    UIButton *btn = [UIButton buttonWithType:UIButtonTypeSystem];
    btn.frame = frame;
    btn.backgroundColor = [UIColor systemGray5Color];
    [btn setTitle:title forState:UIControlStateNormal];
    btn.layer.cornerRadius = 8;
    btn.tag = tag;
    [btn addTarget:self action:@selector(linkAreaButtonTapped:) forControlEvents:UIControlEventTouchUpInside];
    return btn;
}

- (void)linkAreaButtonTapped:(UIButton *)sender {
    // 占位实现, 后续可根据 tag 区分不同跳转
    NSLog(@"Link area button %ld tapped", (long)sender.tag);
}

#pragma mark - Display Image Tap Actions

- (void)leftDisplayImageTapped {
    NSLog(@"Left display image tapped – 触发申请逻辑");
    // 模拟点击申请按钮：直接调用产品准入检查并跳转逻辑
    if (self.largeCardProductInfo) {
        [self precheckAndNavigateWithProduct:self.largeCardProductInfo];
    }
}

- (void)rightDisplayImageTapped {
    NSLog(@"Right display image tapped – 打开客服 H5 页面");
    if (self.customerServiceURL.length > 0) {
        [self openWebWithURLString:self.customerServiceURL];
    } else {
        NSLog(@"客服链接为空，无法打开客服页面");
    }
}

#pragma mark - Protocol Image Tap Action

- (void)protocolImageTapped {
    NSLog(@"Protocol image tapped – 打开协议 H5 页面");
    if (self.agreementURL.length > 0) {
        [self openWebWithURLString:self.agreementURL];
    }
}

#pragma mark - 准入预检查

- (void)precheckAndNavigateWithProduct:(NSDictionary *)productInfo {
    // 保持 HUD 显示，等待网络回调后再隐藏
    UIView *hudSuperView = self.view.window ?: self.view;
    [HUD showLoadingInView:hudSuperView withMessage:@"Checking…"];

    NSString *splendid = productInfo[@"darn"] ?: @"";
    NSDictionary *params = @{ @"splendid": splendid };

    __weak typeof(self) weakSelf = self;
    [NetworkManager postFormWithAPI:@"Alicia/heardmrs" params:params completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            [HUD hideForView:hudSuperView];

            // 网络错误直接拦截，不跳转
            if (error) {
                UIView *hudSuperView = weakSelf.view.window ?: weakSelf.view;
                [HUD showError:error inView:hudSuperView];
                NSLog(@"[HomeVC] 准入接口网络错误，拦截跳转: %@", error);
                return;
            }

            // 检查接口返回状态码
            NSString *modest = [NSString stringWithFormat:@"%@", response[@"modest"]];
            NSLog(@"[HomeVC] 准入接口返回状态码: %@", modest);

            // 处理未登录（modest = -2）
            if ([modest isEqualToString:@"-2"]) {
                UIView *hudSuperView = weakSelf.view.window ?: weakSelf.view;
                [HUD showToast:(response[@"patted"] ?: @"Please login to operate") inView:hudSuperView];
                NSLog(@"[HomeVC] 准入接口返回未登录状态，拦截跳转并弹出登录页");
                // 弹出登录页面
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1.5 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    LoginViewController *loginVC = [[LoginViewController alloc] init];
                    loginVC.modalPresentationStyle = UIModalPresentationFullScreen;
                    [weakSelf presentViewController:loginVC animated:YES completion:nil];
                });
                return;
            }

            // 只有当 modest = 0 时才认为准入成功，允许跳转
            if (![modest isEqualToString:@"0"]) {
                UIView *hudSuperView = weakSelf.view.window ?: weakSelf.view;
                NSString *errorMsg = response[@"patted"] ?: @"Access denied";
                [HUD showToast:errorMsg inView:hudSuperView];
                NSLog(@"[HomeVC] 准入接口返回错误状态码 %@，拦截跳转: %@", modest, errorMsg);
                return;
            }

            NSLog(@"[HomeVC] 准入检查通过，允许跳转");

            // 根据返回结果判断跳转类型
            NSString *toteach = nil;
            if ([response isKindOfClass:[NSDictionary class]]) {
                NSDictionary *awkward = response[@"awkward"];
                if ([awkward isKindOfClass:[NSDictionary class]]) {
                    toteach = awkward[@"toteach"];
                }
                if (!toteach) {
                    toteach = response[@"toteach"]; // 兜底
                }
            }

            if ([toteach isKindOfClass:[NSString class]] && toteach.length > 0) {
                if ([toteach containsString:@"http"]) {
                    [weakSelf openWebWithURLString:toteach];
                } else {
                    [weakSelf navigateToCertificationWithProduct:productInfo route:toteach];
                }
            } else {
                // 默认进入认证页
                [weakSelf navigateToCertificationWithProduct:productInfo route:nil];
            }
        });
    }];
}

- (void)navigateToProductDetailWithProduct:(NSDictionary *)productInfo {
    // 占位跳转逻辑
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:productInfo[@"cando"] ?: @"Product"
                                                                   message:@"进入产品详情 (Placeholder)"
                                                            preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:@"OK" style:UIAlertActionStyleDefault handler:nil]];
    [self presentViewController:alert animated:YES completion:nil];
}

// 获取原始元素类型 (便于调试观察)
- (NSString *)getOriginalElementType:(NSString *)mixedSubject {
    NSDictionary *subjectMap = @{
        @"verylittle": @"BANNER (广告Banner)",
        @"zelle": @"LARGE_CARD (大卡位)",
        @"Rougier": @"SMALL_CARD (小卡位)",
        @"beginning": @"PRODUCT_LIST (极速产品列表)"
    };

    return subjectMap[mixedSubject] ?: @"未知类型";
}

#pragma mark - UITableViewDataSource & UITableViewDelegate

- (NSInteger)tableView:(UITableView *)tableView numberOfRowsInSection:(NSInteger)section {
    // 每个产品单独成为一个 section，便于设置间距
    return 1;
}

- (NSInteger)numberOfSectionsInTableView:(UITableView *)tableView {
    return self.smallCardListProducts.count;
}

- (UITableViewCell *)tableView:(UITableView *)tableView cellForRowAtIndexPath:(NSIndexPath *)indexPath {
    HomeProductCell *cell = [tableView dequeueReusableCellWithIdentifier:@"HomeProductCell" forIndexPath:indexPath];
    NSDictionary *product = self.smallCardListProducts[indexPath.section];
    [cell configureWithProduct:product];
    return cell;
}

- (void)tableView:(UITableView *)tableView didSelectRowAtIndexPath:(NSIndexPath *)indexPath {
    [tableView deselectRowAtIndexPath:indexPath animated:YES];
    NSDictionary *product = self.smallCardListProducts[indexPath.section];
    [self precheckAndNavigateWithProduct:product];
}

- (CGFloat)tableView:(UITableView *)tableView heightForRowAtIndexPath:(NSIndexPath *)indexPath {
    return (self.view.bounds.size.width - 28) * (109.0f / 347.0f);
}

- (CGFloat)tableView:(UITableView *)tableView heightForHeaderInSection:(NSInteger)section {
    return section == 0 ? 0.0001f : 0.0001f; // 极小高度，避免默认间距
}

- (UIView *)tableView:(UITableView *)tableView viewForHeaderInSection:(NSInteger)section {
    return [[UIView alloc] initWithFrame:CGRectZero];
}

- (CGFloat)tableView:(UITableView *)tableView heightForFooterInSection:(NSInteger)section {
    return 10.0f; // cell 之间的间距
}

- (UIView *)tableView:(UITableView *)tableView viewForFooterInSection:(NSInteger)section {
    UIView *footer = [[UIView alloc] initWithFrame:CGRectZero];
    footer.backgroundColor = [UIColor clearColor];
    return footer;
}

#pragma mark - Navigation Helpers

- (void)openWebWithURLString:(NSString *)urlString {
    if (urlString.length == 0) return;
    H5WebViewController *webVC = [[H5WebViewController alloc] initWithURLString:urlString];
    [self.navigationController pushViewController:webVC animated:YES];
}

- (void)navigateToCertificationWithProduct:(NSDictionary *)productInfo route:(NSString *)route {
    ProductsCertificationViewController *vc = [[ProductsCertificationViewController alloc] init];
    vc.splendid = [NSString stringWithFormat:@"%@", productInfo[@"darn"] ?: @""]; // 传递 ID
    [self.navigationController pushViewController:vc animated:YES];
}

#pragma mark - Device Identifier Reporting

- (void)reportDeviceIdentifiers {
    NSString *idfv = [DeviceIDManager persistentIDFV]; // 使用统一的设备ID管理器
    __block NSString *idfa = @"";

    void (^sendRequest)(void) = ^{
        NSDictionary *params = @{ @"picnic": idfv ?: @"",
                                  @"lunch": idfa ?: @"" };
        [NetworkManager postFormWithAPI:@"Alicia/pickles" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
            // 确保在主线程处理结果
            dispatch_async(dispatch_get_main_queue(), ^{
                if (error) {
                    NSLog(@"[Home] 设备 ID 上报失败: %@", error);
                } else {
                    NSLog(@"[Home] 设备 ID 上报成功: %@", response);
                }
            });
        }];
    };

    // 处理 IDFA 获取逻辑
#if TARGET_OS_SIMULATOR
    sendRequest();
#else
    if (@available(iOS 14, *)) {
        ATTrackingManagerAuthorizationStatus status = [ATTrackingManager trackingAuthorizationStatus];
        if (status == ATTrackingManagerAuthorizationStatusNotDetermined) {
            [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    [self reportDeviceIdentifiers];
                });
            }];
            return;
        }
        if (status == ATTrackingManagerAuthorizationStatusAuthorized) {
            idfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString ?: @"";
        }
        sendRequest();
    } else {
        idfa = [ASIdentifierManager sharedManager].advertisingIdentifier.UUIDString ?: @"";
        sendRequest();
    }
#endif
}

#pragma mark - Device Information Reporting

- (void)reportDeviceInfo {
    NSDictionary *infoDict = [DeviceInfoManager collectDeviceInfo];
    NSError *jsonErr;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:infoDict options:0 error:&jsonErr];
    if (jsonErr) {
        NSLog(@"[Home] 设备信息 JSON 序列化失败: %@", jsonErr);
        return;
    }
    NSString *jsonStr = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    NSDictionary *params = @{ @"awkward": jsonStr ?: @"" };

    NSLog(@"[Home] 设备信息上报入参: %@", params);
    
    [NetworkManager postFormWithAPI:@"Alicia/pickle" params:params completion:^(NSDictionary * _Nullable response, NSError * _Nullable error) {
        // 确保在主线程处理结果
        dispatch_async(dispatch_get_main_queue(), ^{
            if (error) {
                NSLog(@"[Home] 设备信息上报失败: %@", error);
            } else {
                NSLog(@"[Home] 设备信息上报成功: %@", response);
            }
        });
    }];
}

// 新增公共方法处理申请按钮点击
- (void)handleApplyForIndex:(NSInteger)index {
    if (index >= 0 && index < self.smallCardListProducts.count) {
        NSDictionary *product = self.smallCardListProducts[index];
        [self precheckAndNavigateWithProduct:product];
    }
}

#pragma mark - 顶部小卡申请按钮

- (void)topApplyTapped {
    NSLog(@"[HomeVC] 小卡样式顶部产品申请被点击 - 可通过背景图或申请按钮触发");
    if (self.smallCardTopProduct) {
        [self precheckAndNavigateWithProduct:self.smallCardTopProduct];
    } else {
        NSLog(@"[HomeVC] 警告：smallCardTopProduct 为空，无法处理申请");
    }
}

// 新增：小卡样式下拉刷新控件初始化
- (void)setupSmallCardRefreshControl {
    if (!self.refreshControl) {
        NSLog(@"[HomeVC] 初始化小卡样式UIRefreshControl");
        self.refreshControl = [[UIRefreshControl alloc] init];
        self.refreshControl.tintColor = [UIColor systemGrayColor];
        [self.refreshControl addTarget:self action:@selector(refreshData) forControlEvents:UIControlEventValueChanged];
    }
}

// 新增：大卡样式下拉刷新控件初始化
- (void)setupLargeCardRefreshControl {
    if (!self.mjRefreshHeader) {
        NSLog(@"[HomeVC] 初始化大卡样式MJRefreshHeader");
        __weak typeof(self) weakSelf = self;
        self.mjRefreshHeader = [MJRefreshNormalHeader headerWithRefreshingBlock:^{
            NSLog(@"[HomeVC] MJRefresh 触发下拉刷新回调");
            [weakSelf refreshData];
        }];

        // 设置菊花颜色为白色，更适合大卡样式的蓝色背景
        self.mjRefreshHeader.loadingView.color = [UIColor whiteColor];

        // 隐藏所有文字，只显示菊花
        self.mjRefreshHeader.stateLabel.hidden = YES;
        self.mjRefreshHeader.lastUpdatedTimeLabel.hidden = YES;

        // 关键设置：启用自动透明度变化，实现只在下拉时显示
        self.mjRefreshHeader.automaticallyChangeAlpha = YES;

        // 初始状态设置为完全透明（不下拉时隐藏）
        self.mjRefreshHeader.alpha = 0.0;

        NSLog(@"[HomeVC] MJRefreshHeader 初始化完成:");
        NSLog(@"[HomeVC] - automaticallyChangeAlpha: %@", self.mjRefreshHeader.automaticallyChangeAlpha ? @"YES" : @"NO");
        NSLog(@"[HomeVC] - 初始alpha: %.2f", self.mjRefreshHeader.alpha);
        NSLog(@"[HomeVC] - 不下拉时完全隐藏，下拉时根据进度显示");
    }
}



// 新增：根据当前布局挂载刷新控件
- (void)attachRefreshControl {
    if (self.isLargeCardStyle) {
        // 大卡样式：使用MJRefresh，在buildLargeCardUI中已经设置
        // 清除可能存在的UIRefreshControl绑定
        if (self.mainScrollView) {
            if (@available(iOS 10.0, *)) {
                self.mainScrollView.refreshControl = nil;
            }
        }
        return;
    }

    // 小卡样式：使用UIRefreshControl
    if (!self.refreshControl || !self.productsTableView) { return; }

    if (@available(iOS 10.0, *)) {
        // 先清除旧的绑定
        if (self.productsTableView.refreshControl == self.refreshControl) {
            // 已经正确绑定，无需重复设置
            return;
        }
        self.productsTableView.refreshControl = self.refreshControl;
    } else {
        // 移除旧的，添加到新的滚动视图
        [self.refreshControl removeFromSuperview];
        [self.productsTableView addSubview:self.refreshControl];
    }
}

// 首页数据请求方法
- (void)fetchHomeData {
    NSLog(@"[HomeVC] fetchHomeData 开始 - 当前样式: %@", self.isLargeCardStyle ? @"大卡" : @"小卡");
    UIView *hudSuperView = self.view.window ?: self.view;
    
    // 判断是否正在下拉刷新
    BOOL isRefreshing = NO;
    if (self.isLargeCardStyle) {
        isRefreshing = self.mjRefreshHeader.isRefreshing;
        NSLog(@"[HomeVC] 大卡样式 - MJRefresh 刷新状态: %@", isRefreshing ? @"刷新中" : @"未刷新");
    } else {
        isRefreshing = self.refreshControl.isRefreshing;
        NSLog(@"[HomeVC] 小卡样式 - UIRefreshControl 刷新状态: %@", isRefreshing ? @"刷新中" : @"未刷新");
    }

    // 非下拉刷新时显示 HUD
    if (!isRefreshing) {
        NSLog(@"[HomeVC] 显示加载HUD");
        [HUD showLoadingInView:hudSuperView withMessage:@"Loading…"];
    } else {
        NSLog(@"[HomeVC] 下拉刷新中，不显示HUD");
    }

    __weak typeof(self) weakSelf = self;
    [NetworkManager requestWithAPI:@"Alicia/roundwarily" params:@{} method:@"GET" completion:^(NSDictionary *response, NSError *error) {
        dispatch_async(dispatch_get_main_queue(), ^{
            NSLog(@"[HomeVC] 网络请求完成，开始处理响应");
            
            // 确保HUD被隐藏
            [HUD hideForView:hudSuperView];

            // 结束下拉刷新
            if (weakSelf.isLargeCardStyle && weakSelf.mjRefreshHeader.isRefreshing) {
                NSLog(@"[HomeVC] 结束大卡样式MJRefresh刷新");
                [weakSelf.mjRefreshHeader endRefreshing];
            } else if (!weakSelf.isLargeCardStyle && weakSelf.refreshControl.isRefreshing) {
                NSLog(@"[HomeVC] 结束小卡样式UIRefreshControl刷新");
                [weakSelf.refreshControl endRefreshing];
            }

            if (error) {
                NSLog(@"[Home] HomeData API (Alicia/roundwarily) 请求失败: %@", error);
                [HUD showError:error inView:hudSuperView];
                return;
            }

            // 解析首页元素类型
            if ([response isKindOfClass:[NSDictionary class]]) {
                NSDictionary *awkward = response[@"awkward"];
                if ([awkward isKindOfClass:[NSDictionary class]]) {
                    NSDictionary *mayonnaise = awkward[@"mayonnaise"];
                    if ([mayonnaise isKindOfClass:[NSDictionary class]]) {
                        weakSelf.customerServiceURL = mayonnaise[@"cardboardcontainers"];
                    }
                    NSDictionary *pickles = awkward[@"pickles"];
                    if ([pickles isKindOfClass:[NSDictionary class]]) {
                        weakSelf.agreementURL = pickles[@"eating"];
                    }
                    NSArray *boasting = awkward[@"boasting"];
                    if ([boasting isKindOfClass:[NSArray class]]) {
                        [weakSelf parseHomeElements:boasting];
                    }
                }
            }
        });
    }];
}

#pragma mark - 位置权限引导

// ===== 位置权限引导方法 BEGIN =====
- (void)showLocationGuideIfNeeded {
    // 1. 判断服务器配置是否要求弹窗
    NSNumber *shouldShowNum = [[NSUserDefaults standardUserDefaults] objectForKey:@"show_location_guide"];
    if (shouldShowNum.integerValue != 1) { return; }

    // 2. 判断定位权限状态，仅在被拒绝或受限时提示
    CLAuthorizationStatus status = [CLLocationManager authorizationStatus];
#if __IPHONE_OS_VERSION_MAX_ALLOWED >= 140000
    if (status != kCLAuthorizationStatusDenied && status != kCLAuthorizationStatusRestricted) { return; }
#else
    if (status == kCLAuthorizationStatusAuthorizedWhenInUse || status == kCLAuthorizationStatusAuthorizedAlways) { return; }
#endif

    // 3. 确保一天只弹一次
    NSDateFormatter *fmt = [[NSDateFormatter alloc] init];
    fmt.dateFormat = @"yyyyMMdd";
    NSString *today = [fmt stringFromDate:[NSDate date]];
    NSString *lastDate = [[NSUserDefaults standardUserDefaults] objectForKey:@"location_guide_last_date"];
    if ([lastDate isEqualToString:today]) { return; }

    // 保存弹窗日期
    [[NSUserDefaults standardUserDefaults] setObject:today forKey:@"location_guide_last_date"];
    [[NSUserDefaults standardUserDefaults] synchronize];

    // 4. 创建并展示弹窗
    UIAlertController *alert = [UIAlertController alertControllerWithTitle:NSLocalizedString(@"Location Permission Needed", nil)
                                                                   message:NSLocalizedString(@"Location access is turned off. Please go to Settings to enable it and proceed with regional validation.", nil)
                                                            preferredStyle:UIAlertControllerStyleAlert];
    [alert addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"Cancel", nil) style:UIAlertActionStyleCancel handler:nil]];
    [alert addAction:[UIAlertAction actionWithTitle:NSLocalizedString(@"Open Settings", nil) style:UIAlertActionStyleDefault handler:^(UIAlertAction * _Nonnull action) {
        NSURL *settingsURL = [NSURL URLWithString:UIApplicationOpenSettingsURLString];
        if ([[UIApplication sharedApplication] canOpenURL:settingsURL]) {
            if (@available(iOS 10.0, *)) {
                [[UIApplication sharedApplication] openURL:settingsURL options:@{} completionHandler:nil];
            } else {
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wdeprecated-declarations"
                [[UIApplication sharedApplication] openURL:settingsURL];
#pragma clang diagnostic pop
            }
        }
    }]];
    [self presentViewController:alert animated:YES completion:nil];
}
// ===== 位置权限引导方法 END =====

#pragma mark - Notification Observers

- (void)setupNotificationObservers {
    NSNotificationCenter *center = [NSNotificationCenter defaultCenter];

    // 监听应用激活状态，用于从后台回到前台时自动刷新数据和上报信息
    if (@available(iOS 13.0, *)) {
        [center addObserver:self
                   selector:@selector(handleSceneDidBecomeActive:)
                       name:UISceneDidActivateNotification
                     object:nil];
    } else {
        [center addObserver:self
                   selector:@selector(handleApplicationDidBecomeActive:)
                       name:UIApplicationDidBecomeActiveNotification
                     object:nil];
    }

    // 监听定位权限变化
    [center addObserver:self
               selector:@selector(handleLocationPermissionGranted:)
                   name:@"LocationPermissionGranted"
                 object:nil];
}

- (void)handleSceneDidBecomeActive:(NSNotification *)notification API_AVAILABLE(ios(13.0)) {
    NSLog(@"[HomeVC] Scene 激活 - 从后台回到前台，自动刷新数据");
    [self handleAppBecomeActiveLocationCheck];
}

- (void)handleApplicationDidBecomeActive:(NSNotification *)notification {
    NSLog(@"[HomeVC] Application 激活 - 从后台回到前台，自动刷新数据");
    [self handleAppBecomeActiveLocationCheck];
}

- (void)handleLocationPermissionGranted:(NSNotification *)notification {
    NSLog(@"[HomeVC] 收到定位权限已授权通知 - 开始上报信息");
    // 权限刚刚被授权，立即上报位置信息、设备标识符和设备信息
    [[LocationManager sharedManager] reportLocationInfo];
    [self reportDeviceIdentifiers];
    [self reportDeviceInfo];
}

- (void)handleAppBecomeActiveLocationCheck {
    NSLog(@"[HomeVC] 从后台回到前台 - 自动调用下拉刷新接口和上报信息");

    // 不管定位权限状态如何，都调用一次下拉刷新的接口和上报信息
    NSLog(@"[HomeVC] 从后台回到前台 - 开始上报位置信息、IDFV & IDFA、设备信息");
    [[LocationManager sharedManager] reportLocationInfo];
    [self reportDeviceIdentifiers];
    [self reportDeviceInfo];

    // 调用下拉刷新接口（获取首页数据）
    NSLog(@"[HomeVC] 从后台回到前台 - 调用下拉刷新接口");
    [self fetchHomeData];
}

@end
