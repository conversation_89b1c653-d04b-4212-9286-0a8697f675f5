# 埋点测试日志说明

## 🔍 快速搜索方法
在Xcode中搜索 `[埋点测试]` 可以快速查看所有埋点上报情况

## 📋 埋点测试日志格式

### 风险事件埋点日志（高敏感度配置）
```
[埋点测试] 注册验证码埋点上报
[埋点测试] 上报参数listener=1
[埋点测试] 上报参数brightly=2
[埋点测试] 上报参数herselfshone=XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
[埋点测试] 上报参数bygwendoline=XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
[埋点测试] 上报用到的经度是113.336576
[埋点测试] 上报用到的纬度是23.123456
[埋点测试] 上报参数pawed=1234567890
[埋点测试] 上报参数letyou=1234567900
[埋点测试] 注册验证码埋点上报成功
```

### 地址埋点日志（高敏感度配置）
```
[埋点测试] 地址埋点上报
[埋点测试] 上报参数darrein=XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX
[埋点测试] 上报参数italways=2
[埋点测试] 上报参数unkindly=中国
[埋点测试] 上报参数askedme=某某街道
[埋点测试] 上报用到的经度是113.336626
[埋点测试] 上报用到的纬度是23.123466
[埋点测试] 上报参数sharethe=深圳市
[埋点测试] 上报参数adoration=南山区
[埋点测试] 地址埋点上报成功
```

## 🎯 高敏感度定位配置

### 关键API参数设置
- **desiredAccuracy**: `kCLLocationAccuracyBestForNavigation` (最高精度)
- **distanceFilter**: `kCLDistanceFilterNone` (不过滤任何距离变化)
- **数据有效期**: `0.1秒` (只接受0.1秒内的新位置)
- **超时时间**: `30秒` (平衡精度和响应速度)
- **最少等待**: `5秒` (让GPS稳定)

### 预期效果（6位小数）
```
[埋点测试] 上报用到的经度是113.336576  ← 第1次埋点
[埋点测试] 上报用到的经度是113.336626  ← 第2次埋点 (小数点后4-5位变化)
[埋点测试] 上报用到的经度是113.336615  ← 第3次埋点 (持续微小变化)
```

# 埋点问题排查和坐标格式修改说明

## 🔍 埋点2（证件类型选择）排查

### 问题描述
测试人员反馈：埋点2没有上报，2-4应该是3-5埋点

### 排查方法

#### 1. 检查埋点2开始时间设置
```
🔥 [埋点2-证件类型选择] 开始时间记录: 1234567890
```
- 出现此日志说明埋点2开始时间已正确设置

#### 2. 检查跳过条件
```
🔥 [埋点2-证件类型选择] 跳过 - 证件正面已完成
```
- 如果证件正面已完成，不会触发埋点2

#### 3. 检查上报条件
```
🔥 [埋点2-证件类型选择] 跳过上报 - 开始时间未设置 (docSelectStartTime=0)
```
- 如果开始时间为0，不会上报埋点2

#### 4. 正常上报日志
```
🔥 [埋点2-证件类型选择] 开始上报 startTime:1234567890 endTime:1234567900
🚨 [RiskEventManager] ===== 风险事件埋点上报开始 =====
🚨 [RiskEventManager] 事件类型: 2
```

### 埋点2触发流程
1. **开始**: 点击证件上报按钮（前提：证件正面未完成）
2. **结束**: 选择完成证件类型
3. **条件**: `self.info.frontCompleted == NO`

### 测试建议
1. 从全新状态开始测试
2. 确保证件正面未完成
3. 按顺序：点击证件按钮 → 选择证件类型

## 📍 坐标格式修改

### 修改内容
所有坐标打印格式从 `%.14f` 改为 `%.6f`

### 修改后的日志格式
```
🚨 [RiskEventManager] 请求前缓存坐标: 31.123457, 121.987654
🚨 [RiskEventManager] 获取实时定位成功 - 坐标: 31.123460, 121.987651
🏠 [LocationManager] 请求前缓存坐标: 31.123457, 121.987654
🏠 [LocationManager] 位置信息上报 - 开始反向地理编码: 31.123460, 121.987651
```

### 验证实时定位效果
观察 `请求前缓存坐标` 和 `获取实时定位成功` 的坐标差异：
- **有差异**: 实时定位正常工作
- **无差异**: 可能使用了缓存坐标

## 🎯 埋点类型对应关系

| 埋点编号 | 枚举值 | 说明 | 触发时机 |
|---------|--------|------|----------|
| 1 | RiskEventTypeRegister | 注册验证码 | 登录成功后 |
| 2 | RiskEventTypeDocumentSelect | 证件类型选择 | 选择证件类型完成 |
| 3 | RiskEventTypeDocumentFront | 证件正面拍照 | 证件上传成功 |
| 4 | RiskEventTypeFaceRecognition | 人脸识别 | 人脸上传成功 |
| 5 | RiskEventTypePersonalInfo | 个人信息 | 个人信息提交成功 |
| 6 | RiskEventTypeJobInfo | 工作信息 | 工作信息提交成功 |
| 7 | RiskEventTypeContacts | 联系人 | 联系人上传成功 |
| 8 | RiskEventTypeBindCard | 绑卡 | 绑卡成功 |
| 9 | RiskEventTypeStartLoan | 开始审贷 | 点击下一步成功 |
| 10 | RiskEventTypeEndLoan | 结束审贷 | H5确认申请 |

## 🔧 关键修改文件

1. **RiskEventManager.m**: 坐标格式修改为 `.6f`
2. **LocationManager.m**: 坐标格式修改为 `.6f`
3. **IdCardAuthenticationViewController.m**: 增加埋点2调试日志

## 🎯 经纬度灵敏度测试方法

### 1. 搜索关键词
在Xcode控制台搜索：`[埋点测试] 上报用到的经度是` 或 `[埋点测试] 上报用到的纬度是`

### 2. 观察坐标变化（6位小数精度）
- **正常情况**: 每次埋点的经纬度都有微小差异
- **异常情况**: 多次埋点经纬度完全相同（说明使用了缓存）
- **预期精度**: 小数点后4-6位应该有变化

### 3. 测试步骤（米级检测）
1. 在客厅沙发触发一个埋点，记录6位小数经纬度
2. 走到餐桌（1-2米距离）触发另一个埋点，记录经纬度
3. 对比两次坐标差异：
   - **经度差异**: 小数点后4-5位应有变化
   - **纬度差异**: 小数点后4-5位应有变化
   - **预期变化**: 0.00001度 ≈ 1米，0.000001度 ≈ 10厘米

### 4. 高敏感度定位特性
- **极短数据有效期**: 只接受0.1秒内的新位置数据
- **30秒超时**: 平衡精度和响应速度
- **无距离过滤**: `kCLDistanceFilterNone` 捕获任何微小移动
- **最高精度**: `kCLLocationAccuracyBestForNavigation`

## 📋 测试检查清单

- [ ] 搜索 `[埋点测试]` 能看到所有埋点上报
- [ ] 埋点2开始时间是否正确设置
- [ ] 埋点2是否正常上报
- [ ] 坐标格式是否为6位小数
- [ ] 实时定位是否有坐标差异
- [ ] 各埋点顺序是否正确（1→2→3→4→5...）
- [ ] 经纬度在不同位置是否有微小变化

## 🔧 关键优化

1. **分行打印**: 避免NSLog截断长参数
2. **统一标识**: 所有埋点都使用 `[埋点测试]` 前缀
3. **坐标格式**: 统一使用 `.6f` 格式（6位小数）
4. **参数明确**: 单独打印经度和纬度，便于快速检查
